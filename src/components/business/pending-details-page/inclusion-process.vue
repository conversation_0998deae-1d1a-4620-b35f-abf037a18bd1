<template>
    <n-descriptions
        label-placement="left"
        label-align="right"
        label-style="width: 110px;"
        bordered
        :column="2"
        size="small"
    >
        <n-descriptions-item label="申请人" :span="2"> {{ modelValue.nickname }} </n-descriptions-item>
        <n-descriptions-item label="申请原因" :span="2"> {{ modelValue.reason }} </n-descriptions-item>
        <template v-for="item in modelValue.data" :key="item.id">
            <n-descriptions-item label="集团文件编号"> {{ item.fileNo }} </n-descriptions-item>
            <n-descriptions-item label="集团文件名称"> {{ item.fileName }} </n-descriptions-item>
            <n-descriptions-item label="原文件编号"> {{ item.originalNumber }} </n-descriptions-item>
            <n-descriptions-item label="原文件版次"> {{ item.originalVersion }} </n-descriptions-item>
        </template>
    </n-descriptions>
</template>
<script setup lang="ts">
const props = withDefaults(
    defineProps<{
        modelValue?: any;
    }>(),
    {
        modelValue: ''
    }
);
const emit = defineEmits(['update:modelValue']);
const { modelValue } = useVModels(props, emit);
</script>
<style scoped lang="less"></style>
