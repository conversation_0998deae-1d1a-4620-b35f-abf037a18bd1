<template>
    <div>
        <n-form :model="form" class="useFormDialog" label-placement="left" :show-feedback="false" :label-width="110">
            <n-grid :cols="24" :x-gap="10" :y-gap="10">
                <n-form-item-gi label="书籍名称" :span="12">
                    <n-input v-model:value="form.row.name" />
                </n-form-item-gi>
                <n-form-item-gi label="作者/编者" :span="12">
                    <n-input v-model:value="form.row.author" />
                </n-form-item-gi>
                <n-form-item-gi label="出版社" :span="12">
                    <n-input v-model:value="form.row.publisher" />
                </n-form-item-gi>
                <n-form-item-gi label="书籍类别" :span="12">
                    <n-input v-model:value="form.row.bookType" />
                </n-form-item-gi>
                <n-form-item-gi label="领用原因" :span="24">
                    <n-input v-model:value="form.reason" type="textarea"  />
                </n-form-item-gi>
            </n-grid>
        </n-form>
    </div>
</template>
<script setup lang="ts">
const props = defineProps<{
    modelValue: any;
}>();
const form = props.modelValue;
</script>
