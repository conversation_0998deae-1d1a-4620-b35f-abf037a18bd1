<template>
    <div class="file-preview flex justify-center items-center w-100% h-100%">
        <n-watermark
            class="w-100%"
            content="本文件涉密 禁止拍照或截图"
            :remarks="remarks"
            cross
            selectable
            :font-size="16"
            :line-height="50"
            :width="500"
            :height="200"
            :x-offset="-10"
            :y-offset="20"
            :re-x-offset="330"
            :re-y-offset="240"
            :rotate="-15"
            font-color="rgba(128,128,128,.15)"
        >
            <iframe
                class="h-100vh w-100%"
                :src="fileUrl"
                width="100%"
                height="100%"
                @error="handleError"
                @load="handleLoad"
            ></iframe>
            <n-spin v-if="loading" :show="true" class="abs w-100% h-100% flex justify-center items-center"> </n-spin>
        </n-watermark>
    </div>
</template>

<script setup lang="ts">
import useStore from '@/store/modules/main';
import dayjs from 'dayjs';

const store = useStore();
const route = useRoute();

const fileId = ref('');
const user = ref('');
const remarks = computed(() => {
    return user.value + dayjs().format('YYYY-MM-DD HH:mm:ss');
});

const fileUrl = ref<string>('');
const loading = ref<boolean>(true);
const error = ref<string>('');

const handleError = () => {
    error.value = '文件加载失败，请稍后重试';
    loading.value = false;
};

const handleLoad = () => {
    loading.value = false;
};

const getFileUrl = async () => {
    try {
        loading.value = true;
        error.value = '';
        fileUrl.value = `${
            import.meta.env.VITE_API || window.location.origin
        }/macrohard/api/v1/word/onlinePreview?fileId=${fileId.value}`;
    } catch (err) {
        window.$message.error('获取文件预览地址失败');
        loading.value = false;
    }
};

onMounted(async () => {
    if (route.query.token) {
        await store.setToken(route.query.token as string);
    }
    if (route.query.fileId) {
        fileId.value = route.query.fileId as string;
    }
    if (route.query.user) {
        user.value = route.query.user as string;
    }
    getFileUrl();
});
</script>

<style scoped>
.file-preview {
    background-color: #f5f5f5;
    border-radius: 4px;
    position: relative;
}
</style>
