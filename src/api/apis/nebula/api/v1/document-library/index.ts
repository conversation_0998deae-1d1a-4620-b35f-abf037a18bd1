export default {
    export(data: ExportRequest) {
        return request({
            url: '/nebula/api/v1/document-library/export',
            method: 'post',
            data
        });
    }
}

export interface ExportRequest {
    /**
     * 模块类型，1-书籍库 2-内部库 3-外部库 4 发放回收  5 借阅
     */
    moduleType: number;
    /**
     * 列表请求参数
     */
    params: { [key: string]: any };
    [property: string]: any;
}