export default {

    distributeRecords(params: RecordsRequest) {
        return request({
            url: '/nebula/api/v1/document-library/document/distribute-records',
            method: 'get',
            params
        });
    },
    paperDownloadRecords(params: RecordsRequest) {
        return request({
            url: '/nebula/api/v1/document-library/document/internal-paper-download-records',
            method: 'get',
            params
        });
    },
    electronicDownloadRecords(params: RecordsRequest) {
        return request({
            url: '/nebula/api/v1/document-library/document/external-electronic-download-records',
            method: 'get',
            params
        });
    },
    borrowRecords(params: RecordsRequest) {
        return request({
            url: '/nebula/api/v1/document-library/document/loans/records',
            method: 'get',
            params
        });
    }

};


export interface RecordsRequest {
    /**
     * 文档ID
     */
    documentId: string;
    /**
     * 版本版次
     */
    documentVersionNo?: string;
    /**
     * 页码（从1开始）
     */
    page?: number;
    /**
     * 每页大小
     */
    pageSize?: number;
    /**
     * 是否不需要分页
     */
    noPage?: boolean;
    [property: string]: any;
}
