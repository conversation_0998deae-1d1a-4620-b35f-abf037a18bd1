export default {
    getBookList(data: BookListParams) {
        return request({
            url: '/nebula/api/v1/book/list',
            method: 'post',
            data
        });
    },
    createBook(data: any) {
        return request({
            url: '/nebula/api/v1/book/create',
            method: 'post',
            data
        });
    },
    uploadBook(data: any) {
        return request({
            url: '/nebula/api/v1/book/update',
            method: 'post',
            data
        });
    },
    deleteBook(id: string) {
        return request({
            url: `/nebula/api/v1/book/delete`,
            method: 'post',
            data: {
                id
            }
        });
    },
    importBook(data: {
        dictionaryId: string;
        excelId: string;
        fileInfo: Array<{ fileId: string; fileName: string }> | null;
    }) {
        return request({
            url: '/nebula/api/v1/book/import',
            method: 'post',
            data
        });
    },
    // 归还书籍
    giveBackBook(data: giveBackBookParams) {
        return request({
            url: '/nebula/api/v1/book/give-back',
            method: 'post',
            data
        });
    },
    // 领用借用验重
    plagiarismCheck(params: plagiarismCheckParams) {
        return request({
            url: '/nebula/api/v1/book/plagiarism-check',
            method: 'get',
            params
        });
    }
};
export interface BookRow {
    id: string;
    number?: string;
    name?: string;
    author?: string;
    publisher?: string;
    registerCount?: number;
    receiveCount?: number;
    fileId?: string;
    borrowCount?: number;
    dictionaryNodeId?: string | null;
    bookFileInfo?: any;
}
export interface BookFormData {
    number: string;
    name: string;
    author: string;
    publisher: string;
    fileId: string;
    registerCount: number | null;
    dictionaryNodeId: string | null;
    file?: any;
    remark?: string;
}
export interface BookListParams {
    number: string;
    name: string;
    author: string;
    publisher: string;
    dictionaryNodeIds: any;
    borrowStatus: string;
    page: number;
    pageSize: number;
}
export interface giveBackBookParams {
    bookId: string;
    giveBackType: number; // 归还类型  1借用归还 | 2领用归还
}
export interface plagiarismCheckParams {
    bookId: string;
    plagiarismCheckType: number; // 归还类型  1借用归还 | 2领用归还
}