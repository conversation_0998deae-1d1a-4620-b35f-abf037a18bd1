<template>
    <alert-content :on-default-save="onSubmit">
        <n-form
            ref="formRef"
            label-align="right"
            :model="form"
            :rules="rules"
            label-placement="left"
            label-width="80px"
            require-mark-placement="left"
        >
            <n-form-item :label="`${props.type === 'receive' ? '领用' : '借用'}原因`" path="reason">
                <n-input
                    v-model:value="form.reason"
                    type="textarea"
                    maxlength="50"
                    show-count
                    :placeholder="`请输入${props.type === 'receive' ? '领用' : '借用'}原因`"
                    clearable
                />
            </n-form-item>
        </n-form>
    </alert-content>
</template>
<script setup lang="ts">
import { BookRow } from '@/api/apis/nebula/api/v1/book';

const props = defineProps<{
    row: BookRow;
    type: 'receive' | 'borrow';
}>();

const form = ref({
    reason: ''
});
const rules = ref({
    reason: [{ required: true, message: '请输入领用/借用原因', trigger: 'blur' }]
});
const formRef = ref();

const onSubmit = async () => {
    try {
       await formRef.value?.validate();
    } catch (err: any) {
        window.$message.error(err[0][0].message);
        return Promise.reject();
    }
    await new Promise((resolve) => {
        const formData = JSON.stringify({
            businessId: props.type === 'receive' ? 'FILE_BOOK_COLLECT' : 'FILE_BOOK_BORROW',
            version: '1.0.0',
            data: {
                reason: form.value.reason,
                bookId: props.row.id,
                row: props.row
            }
        });
        window.$dialog.warning({
            title: '确认提示',
            content: `确认后将${props.type === 'receive' ? '领用' : '借用'}书籍，是否确认？`,
            positiveText: '确认',
            negativeText: '取消',
            onPositiveClick: async () => {
                console.log(props.type, 'props.type');
                if (props.type === 'receive') {
                    await $hooks.useApprovalProcess('FILE_BOOK_COLLECT', formData);
                } else {
                    await $hooks.useApprovalProcess('FILE_BOOK_BORROW', formData);
                }
                window.$message.success(`书籍${props.row.name}${props.type === 'receive' ? '领用' : '借用'}流程申请成功`);
                resolve(true)
            }
        });
    });
};
</script>
<style scoped lang="less"></style>
