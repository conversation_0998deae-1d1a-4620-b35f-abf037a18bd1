<template>
    <alert-content :on-default-save="onSubmit">
        <n-form
            ref="formRef"
            :model="formData"
            :rules="rules"
            label-placement="left"
            require-mark-placement="left"
            :label-width="90"
            :show-feedback="false"
        >
            <n-grid :cols="24" :y-gap="10" :x-gap="10">
                <n-form-item-gi v-if="row" label="书籍编号" path="number" :span="12">
                    <n-input v-model:value="formData.number" placeholder="无需填写" readonly />
                </n-form-item-gi>
                <n-form-item-gi label="作者/编者" path="author" :span="12">
                    <n-input
                        v-model:value="formData.author"
                        maxlength="50"
                        show-count
                        placeholder="请输入作者/编者"
                        clearable
                        :disabled="isBorrowOrReceive"
                    />
                </n-form-item-gi>
                <n-form-item-gi label="书籍名称" path="name" :span="12">
                    <n-input
                        v-model:value="formData.name"
                        maxlength="50"
                        show-count
                        placeholder="请输入书籍名称"
                        clearable
                        :disabled="isBorrowOrReceive"
                    />
                </n-form-item-gi>
                <n-form-item-gi label="出版社" path="publisher" :span="12">
                    <n-input
                        v-model:value="formData.publisher"
                        maxlength="50"
                        show-count
                        placeholder="请输入出版社"
                        clearable
                        :disabled="isBorrowOrReceive"
                    />
                </n-form-item-gi>
                <n-form-item-gi label="总在册数" path="registerCount" :span="12">
                    <n-input-number
                        :style="{ width: '100%' }"
                        v-model:value="formData.registerCount"
                        placeholder="请输入总在册数"
                        :min="1"
                        :precision="0"
                        :show-button="false"
                        clearable
                    />
                </n-form-item-gi>
                <n-form-item-gi label="书籍类别" path="dictionaryNodeId" :span="12">
                    <n-tree-select
                        class="w-full"
                        v-model:value="formData.dictionaryNodeId"
                        :options="categoryOptions"
                        filterable
                        clearable
                        :show-path="true"
                        maxTagCount="responsive"
                        placeholder="选择书籍类别"
                    />
                </n-form-item-gi>
                <n-form-item-gi label="上传文件" path="file" :span="24">
                    <minio-upload
                        class="w-full"
                        v-model:file-list="formData.file"
                        :uploadProps="{
                            max: 1,
                            accept: '.pdf',
                            showRemoveButton: !isBorrowOrReceive
                        }"
                        @success="handleFileSuccess"
                    >
                        <template #drag>
                            <div class="w-full h-full flex-v justify-center items-center p-20px">
                                <img :src="uploadFileImg" class="w-50px h-50px mt-10px" />
                                <span class="text-14px mt-10px">点击或者拖动文件到该区域来上传</span>
                                <span class="text-12px c-#838383 mt-10px">只支持上传.pdf文件</span>
                            </div>
                        </template>
                    </minio-upload>
                </n-form-item-gi>
                <n-form-item-gi label="备注" :span="24">
                    <n-input v-model:value="formData.remark" placeholder="请输入备注内容" />
                </n-form-item-gi>
            </n-grid>
        </n-form>
    </alert-content>
</template>

<script setup lang="ts">
import { FormInst, FormRules } from 'naive-ui';
import { BookRow, BookFormData } from '@/api/apis/nebula/api/v1/book';
import uploadFileImg from '@/assets/images/file/upload-file.webp';

const formRef = ref<FormInst>();
const props = defineProps<{
    oId?: string;
    row?: BookRow | boolean;
    categoryOptions: any[];
}>();
const emit = defineEmits(['submit']);
const fileId = ref('');

const formData = reactive<BookFormData & { categoryPath?: string; categoryCodePath?: string }>({
    number: '',
    author: '',
    name: '',
    publisher: '',
    registerCount: null,
    dictionaryNodeId: null,
    file: [],
    fileId: '',
    categoryPath: '',
    categoryCodePath: '',
    remark: ''
});

const categoryOptions = props.categoryOptions;

const rules = computed<FormRules>(() => ({
    author: [{ required: true, message: '请输入作者/编者', trigger: 'blur' }],
    name: [{ required: true, message: '请输入书籍名称', trigger: 'blur' }],
    publisher: [{ required: true, message: '请输入出版社', trigger: 'blur' }],
    registerCount: [
        {
            required: true,
            async validator(_rule, value) {
                if (value === null || value === undefined || value === '') {
                    return Promise.reject('请输入总在册数');
                }
                if (typeof value !== 'number' || value < 1) {
                    return Promise.reject('总在册数不能小于1');
                }
                return Promise.resolve();
            },
            trigger: ['blur', 'change']
        }
    ],
    dictionaryNodeId: [{ required: true, message: '请选择书籍类别', trigger: ['blur', 'change'] }]
}));

// 文件上传成功处理
const handleFileSuccess = (file: any) => {
    fileId.value = file.id;
};

const getEditData = async () => {
    if (props.row && typeof props.row === 'object') {
        Object.assign(formData, props.row);
        formData.dictionaryNodeId = props.row.dictionaryNodeId ?? null;
        formData.fileId = props.row.bookFileInfo.fileId;
        if (props.row.bookFileInfo.fileId) {
            formData.file = [
                { id: props.row.bookFileInfo.fileId, name: props.row.bookFileInfo.fileName, status: 'finished' }
            ];

            fileId.value = props.row.bookFileInfo.fileId || '';
        }
    }
};

// 判断是否有借出和领用
const isBorrowOrReceive = computed(() => {
    if (typeof props.row === 'object' && props.row) {
        const row = props.row as BookRow;
        return (row.borrowCount ?? 0) > 0 || (row.receiveCount ?? 0) > 0;
    }
    return false;
});

// 提交表单
const onSubmit = async () => {
    await formRef.value
        ?.validate()
        .then(async () => {
            await new Promise((resolve) => {
                window.$dialog.warning({
                    title: '确认提示',
                    content: `确认后将${props.row ? '修改' : '新增'}书籍，是否确认？`,
                    positiveText: '确认',
                    negativeText: '取消',
                    onPositiveClick: async () => {
                        formData.fileId = fileId.value ?? formData.fileId;
                        if (props.row) {
                            await $apis.nebula.api.v1.book.uploadBook(formData);
                        } else {
                            await $apis.nebula.api.v1.book.createBook(formData);
                        }
                        window.$message.success(`${props.row ? '修改' : '新增'}成功`);
                        emit('submit');
                        resolve(true);
                    }
                });
            });
        })
        .catch((err: any) => {
            window.$message.error(err[0][0].message);
            return Promise.reject();
        });
};
onMounted(() => {
    if (props.row) {
        getEditData();
    }
});
</script>

<style scoped lang="less">
:deep(.n-upload-file-list) {
    margin-top: 0;
}
</style>
