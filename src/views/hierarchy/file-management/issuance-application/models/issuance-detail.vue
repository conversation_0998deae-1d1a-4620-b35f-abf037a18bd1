<template>
    <alert-content :showDefaultButtons="false">
        <n-space vertical>
            <n-descriptions :column="2" label-placement="left" label-style="width: 110px;" bordered size="small">
                <n-descriptions-item label="发放人">{{ row.applicant }}</n-descriptions-item>
                <n-descriptions-item label="发放日期">
                    {{ dayjs(row.applyDate).format('YYYY-MM-DD') }}
                </n-descriptions-item>
                <n-descriptions-item label="发放类型">
                    {{ issuanceTypeOptions.find((item) => item.value === row.distributeType)?.label }}
                </n-descriptions-item>
                <n-descriptions-item label="文件类型">
                    {{ fileTypeOptions.find((item) => item.value === row.fileType)?.label }}
                </n-descriptions-item>
                <n-descriptions-item label="文件类别" :span="2">{{ row.fileCategory }}</n-descriptions-item>
                <n-descriptions-item label="发放原因">
                    <n-ellipsis>{{ row.reason }}</n-ellipsis>
                </n-descriptions-item>
                <n-descriptions-item label="其他原因" v-if="row.otherReason">{{ row.otherReason }}</n-descriptions-item>
                <n-descriptions-item label="希望发放日期" v-if="row.wishDistributeDate">{{
                    dayjs(row.wishDistributeDate).format('YYYY-MM-DD')
                }}</n-descriptions-item>
            </n-descriptions>
            <p class="text-14px bold">发放清单</p>
            <n-search-table-page
                :data-table-props="{
                    columns,
                    data: tableData,
                    size: 'small',
                    bordered: true,
                    pagination: false,
                    scrollX: 2000
                }"
                :search-props="{
                    show: false
                }"
                :table-props="{
                    showPagination: false
                }"
                padding="0"
            >
                <template #table_eFileLook="{ row }">
                    <div style="line-height: 1.5">
                        <template v-for="(item, index) in row.eFileLook?.receivedBy || []" :key="index">
                            <span v-if="item.status <= 2">{{ item.nickname }}</span>
                            <n-tooltip v-else trigger="hover" placement="top">
                                <template #trigger>
                                    <span style="color: #1890ff; text-decoration: underline; cursor: pointer">
                                        {{ item.nickname }}
                                    </span>
                                </template>
                                <span>回收日期：{{ formatRecycleDate(item.recycleDate) }}</span>
                            </n-tooltip>
                            <span v-if="index < (row.eFileLook?.receivedBy?.length || 0) - 1">，</span>
                        </template>
                        <span v-if="row.recipient && row.eFileLook.receivedBy" style="margin-left: 8px">{{
                            '(' + row.recipient + ')'
                        }}</span>
                    </div>
                </template>
                <template #table_eFileLookAndDownload="{ row }">
                    <div style="line-height: 1.5">
                        <template v-for="(item, index) in row.eFileLookAndDownload?.receivedBy || []" :key="index">
                            <span v-if="item.status <= 2">{{ item.nickname }}</span>
                            <n-tooltip v-else trigger="hover" placement="top">
                                <template #trigger>
                                    <span style="color: #1890ff; text-decoration: underline; cursor: pointer">
                                        {{ item.nickname }}
                                    </span>
                                </template>
                                <span>回收日期：{{ formatRecycleDate(item.recycleDate) }}</span>
                            </n-tooltip>
                            <span v-if="index < (row.eFileLookAndDownload?.receivedBy?.length || 0) - 1">，</span>
                        </template>
                        <span v-if="row.recipient && row.eFileLookAndDownload?.receivedBy" style="margin-left: 8px">{{
                            '(' + row.recipient + ')'
                        }}</span>
                    </div>
                </template>
                <template #table_paperDocumentOnceDownload="{ row }">
                    <div style="line-height: 1.5">
                        <template v-for="(item, index) in row.paperDocumentOnceDownload?.receivedBy || []" :key="index">
                            <span v-if="item.status <= 2">{{ item.nickname }}</span>
                            <n-tooltip v-else trigger="hover" placement="top">
                                <template #trigger>
                                    <span style="color: #1890ff; text-decoration: underline; cursor: pointer">
                                        {{ item.nickname }}
                                    </span>
                                </template>
                                <span>回收日期：{{ formatRecycleDate(item.recycleDate) }}</span>
                            </n-tooltip>
                            <span v-if="index < (row.paperDocumentOnceDownload?.receivedBy?.length || 0) - 1">，</span>
                        </template>
                        <span
                            v-if="row.recipient && row.paperDocumentOnceDownload?.receivedBy"
                            style="margin-left: 8px"
                            >{{ '(' + row.recipient + ')' }}</span
                        >
                    </div>
                </template>
                <template #table_eFileOnceDownload="{ row }">
                    <div style="line-height: 1.5">
                        <template v-for="(item, index) in row.eFileOnceDownload?.receivedBy || []" :key="index">
                            <span v-if="item.status <= 2">{{ item.nickname }}</span>
                            <n-tooltip v-else trigger="hover" placement="top">
                                <template #trigger>
                                    <span style="color: #1890ff; text-decoration: underline; cursor: pointer">
                                        {{ item.nickname }}
                                    </span>
                                </template>
                                <span>回收日期：{{ formatRecycleDate(item.recycleDate) }}</span>
                            </n-tooltip>
                            <span v-if="index < (row.eFileOnceDownload?.receivedBy?.length || 0) - 1">，</span>
                        </template>
                        <span v-if="row.recipient && row.eFileOnceDownload?.receivedBy" style="margin-left: 8px">{{
                            '(' + row.recipient + ')'
                        }}</span>
                    </div>
                </template>
                <template #table_action="{ row }">
                    <n-button @click="showRecycleDialog(row)" size="tiny" type="primary">回收记录</n-button>
                </template>
            </n-search-table-page>
            <span v-if="row.status != 1" style="font-weight: bold" class="my-20px">发放审批记录</span>
            <!-- 审批记录部分 -->
            <div class="approval-record">
                <div
                    v-if="!approvalRecords || approvalRecords.length === 0"
                    class="no-data text-center text-#999 py-20px"
                >
                    暂无审批记录
                </div>
                <div v-else class="approval-list">
                    <div v-for="(item, index) in approvalRecords" :key="index" class="approval-item py-8px">
                        <div class="approval-row flex items-center gap-10px">
                            <span class="text-14px text-#666">{{ item.roleLabel }}</span>
                            <span class="text-14px font-medium">{{ item.approverName }}</span>
                            <span class="text-14px text-#666 ml-20px">{{ item.opinionLabel }}</span>
                            <span class="text-14px">{{ item.opinion }}</span>
                            <span class="text-14px text-#666 ml-20px">{{ item.dateLabel }}</span>
                            <span class="text-14px">{{ item.date }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </n-space>
    </alert-content>
</template>

<script setup lang="ts">
import dayjs from 'dayjs';

interface ApprovalRecord {
    roleLabel: string; // 角色标签：审核人、批准人
    approverName: string; // 审批人姓名
    opinionLabel: string; // 意见标签：审核意见、批准意见
    opinion: string; // 审批意见
    dateLabel: string; // 日期标签：审核日期、批准日期
    date: string; // 审批日期
}

const props = defineProps({
    row: { type: Object, default: () => ({}) }
});
const workflowDetail = ref<any>([]);
const tableData = ref<any[]>([]);
const approvalRecords = ref<ApprovalRecord[]>([]);
const issuanceTypeOptions = [
    { label: '内部发放', value: 1 },
    { label: '外部发放', value: 2 }
];
const fileTypeOptions = [
    { label: '内部文件', value: 1 },
    { label: '外部文件', value: 2 }
];

const getWorkflowDetail = async (id: string) => {
    const res = await api.sass.api.v1.workflow.workflow.detail(id);

    if (!res.data) {
        throw new Error('获取审批详情失败');
    }
    workflowDetail.value = res.data.nodes.filter((item: any) => item.status != 'notStarted');

    // 处理审批记录数据
    processApprovalRecords(res.data);
};

// 格式化日期
const formatDate = (timestamp: number | string) => {
    if (!timestamp) return '';
    return dayjs(timestamp).format('YYYY-MM-DD');
};

// 处理审批记录数据
const processApprovalRecords = (flowData: any) => {
    const records: ApprovalRecord[] = [];

    if (!flowData || !flowData.nodes) {
        approvalRecords.value = records;
        return;
    }

    const nodes = flowData.nodes || [];
    const nodeCount = nodes.length;

    nodes.forEach((node: any, index: number) => {
        const isLast = index === nodeCount - 1;

        if (node.approvers && node.approvers.length > 0) {
            // 检查节点状态，如果是进行中且是最后一个节点，不显示批准人
            const nodeInProgress = node.approvers.some(
                (approver: any) => approver.status !== 'passed' && approver.status !== 'rejected'
            );

            if (node.signingKind === 'or') {
                // 或签：所有审批人合并显示在一行
                const approverNames = node.approvers.map((approver: any) => approver.approverNickname || '').join('、');
                const passedApprover = node.approvers.find((approver: any) => approver.status === 'passed');
                const rejectedApprover = node.approvers.find((approver: any) => approver.status === 'rejected');

                let opinion = '审核中';
                let updateTime = 0;

                if (passedApprover) {
                    opinion = '同意';
                    updateTime = passedApprover.updatedAt;
                } else if (rejectedApprover) {
                    opinion = '驳回';
                    updateTime = rejectedApprover.updatedAt;
                }

                // 对于单个流程的或签，先添加审核人记录
                if (nodeCount === 1) {
                    // 添加审核人记录
                    records.push({
                        roleLabel: '审核人',
                        approverName: approverNames,
                        opinionLabel: '审核意见',
                        opinion,
                        dateLabel: '审核日期',
                        date: formatDate(updateTime) || '--'
                    });

                    // 如果已完成，添加批准人记录
                    if (!nodeInProgress) {
                        records.push({
                            roleLabel: '批准人',
                            approverName: approverNames,
                            opinionLabel: '审核意见',
                            opinion,
                            dateLabel: '批准日期',
                            date: formatDate(updateTime) || '--'
                        });
                    }
                } else {
                    // 多个流程：根据是否是最后一个节点判断角色
                    const roleLabel = isLast ? '批准人' : '审核人';
                    const dateLabel = isLast ? '批准日期' : '审核日期';

                    records.push({
                        roleLabel,
                        approverName: approverNames,
                        opinionLabel: '审核意见',
                        opinion,
                        dateLabel,
                        date: formatDate(updateTime) || '--'
                    });
                }
            } else {
                // 会签：每个审批人单独显示一行
                node.approvers.forEach((approver: any, approverIndex: number) => {
                    // 对于单个流程的会签，第一个人是审核人，后续的人是批准人
                    // 对于多个流程，根据是否是最后一个节点来判断
                    let roleLabel: string;
                    let dateLabel: string;

                    if (nodeCount === 1) {
                        // 单个流程：第一个审批人是审核人，其他是批准人
                        roleLabel = approverIndex === 0 ? '审核人' : '批准人';
                        dateLabel = approverIndex === 0 ? '审核日期' : '批准日期';
                    } else {
                        // 多个流程：根据是否是最后一个节点判断
                        roleLabel = isLast ? '批准人' : '审核人';
                        dateLabel = isLast ? '批准日期' : '审核日期';
                    }

                    const opinionLabel = '审核意见';
                    const opinion =
                        approver.status === 'passed' ? '同意' : approver.status === 'rejected' ? '驳回' : '进行中';

                    records.push({
                        roleLabel,
                        approverName: approver.approverNickname || '',
                        opinionLabel,
                        opinion,
                        dateLabel,
                        date: formatDate(approver.updatedAt) || '--'
                    });
                });
            }
        }
    });

    approvalRecords.value = records;
};

// 从row数据处理审批记录
const processApprovalRecordsFromRow = (row: any) => {
    const records: ApprovalRecord[] = [];

    if (!row) {
        approvalRecords.value = records;
        return;
    }

    // 处理发放审批信息
    if (row.distributeApprovalInfo) {
        const info = row.distributeApprovalInfo;
        const hasApprovers = info.approvers && info.approvers.length > 0;
        const hasAuditors = info.auditors && info.auditors.length > 0;

        // 如果只有审核人没有批准人，审核人既是审核人也是批准人
        if (hasApprovers && !hasAuditors) {
            info.approvers.forEach((approver: any) => {
                // 添加审核人记录
                records.push({
                    roleLabel: '审核人',
                    approverName: approver.userNickname || '',
                    opinionLabel: '审核意见',
                    opinion: approver.passedDate ? '同意' : '进行中',
                    dateLabel: '审核日期',
                    date: formatDate(approver.passedDate)
                });

                // 添加批准人记录（同一个人）
                records.push({
                    roleLabel: '批准人',
                    approverName: approver.userNickname || '',
                    opinionLabel: '审核意见',
                    opinion: approver.passedDate ? '同意' : '进行中',
                    dateLabel: '批准日期',
                    date: formatDate(approver.passedDate)
                });
            });
        } else {
            // 有审核人和批准人的情况
            // 添加审核人
            if (hasApprovers) {
                info.approvers.forEach((approver: any) => {
                    records.push({
                        roleLabel: '审核人',
                        approverName: approver.userNickname || '',
                        opinionLabel: '审核意见',
                        opinion: approver.passedDate ? '同意' : '进行中',
                        dateLabel: '审核日期',
                        date: formatDate(approver.passedDate)
                    });
                });
            }

            // 添加批准人
            if (hasAuditors) {
                info.auditors.forEach((auditor: any) => {
                    records.push({
                        roleLabel: '批准人',
                        approverName: auditor.userNickname || '',
                        opinionLabel: '审核意见',
                        opinion: auditor.passedDate ? '同意' : '进行中',
                        dateLabel: '批准日期',
                        date: formatDate(auditor.passedDate)
                    });
                });
            }
        }
    }

    approvalRecords.value = records;
};

const columns = [
    {
        title: '序号',
        key: 'index',
        width: 60,
        render: (_: any, index: number) => {
            return `${index + 1}`;
        }
    },
    { title: '文件名称', key: 'fileName', width: 100, fixed: 'left', ellipsis: { tooltip: true } },
    { title: '文件编号', key: 'number', width: 100 },
    { title: '版本/版次', key: 'version', width: 100 },
    {
        title: '内发：电子文件-查阅',
        key: 'eFileLook',
        width: 200
    },
    {
        title: '内发：电子文件-查阅/下载',
        key: 'eFileLookAndDownload',
        width: 200
    },
    {
        title: '内发：纸质文件-一次下载',
        key: 'paperDocumentOnceDownload',
        width: 200
    },
    {
        title: '外发：电子文件-一次下载',
        key: 'eFileOnceDownload',
        width: 200
    },
    {
        title: '操作',
        key: 'action',
        width: 80,
        fixed: 'right' as const,
        align: 'center' as const
    }
];

const formatRecycleDate = (date: string | null) => {
    if (!date) return '暂无';
    return dayjs(date).format('YYYY-MM-DD');
};

onMounted(async () => {
    const res = await $apis.nebula.api.v1.issuanceApplication.getDistributeInventory(props.row.id);
    tableData.value = res.data.data;

    // 处理审批记录
    if (props.row.status != 1) {
        // 如果有workflowId，通过API获取审批详情
        if (props.row.workflowId) {
            getWorkflowDetail(props.row.workflowId);
        } else {
            // 否则尝试从row数据中获取审批信息
            processApprovalRecordsFromRow(props.row);
        }
    }
});

const showRecycleDialog = (row: any) => {
    $alert.dialog({
        title: '回收记录',
        content: import('./recycle-record.vue'),
        width: '60%',
        props: {
            id: row.id
        }
    });
};
</script>
<style scoped>
.n-step-description {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.approval-record {
    .title {
        border-bottom: 1px solid #e8e8e8;
        padding-bottom: 8px;
    }

    .approval-item {
        transition: all 0.2s ease;

        &:hover {
            border-color: #d0d0d0 !important;
            background-color: #f5f5f5 !important;
        }
    }

    .approval-row {
        white-space: nowrap;
    }

    .no-data {
        font-size: 14px;
        color: #999;
    }
}
</style>
