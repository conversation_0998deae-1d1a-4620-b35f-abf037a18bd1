<template>
    <alert-content :on-default-save="submit">
        <n-form
            ref="formRef"
            :model="data"
            :rules="rules"
            label-placement="left"
            require-mark-placement="left"
            :label-width="110"
            :show-feedback="false"
            :disabled="isDetail"
        >
            <n-grid :cols="24" :y-gap="10" :x-gap="10">
                <n-form-item-gi label="文件名称" path="name" :span="24">
                    <n-input v-model:value="data.name" placeholder="请输入文件名称" maxlength="50" show-count />
                </n-form-item-gi>
                <n-form-item-gi label="文件英文名称" :span="24">
                    <n-input
                        v-model:value="data.englishName"
                        placeholder="请输入文件英文名称"
                        maxlength="400"
                        show-count
                    />
                </n-form-item-gi>
                <n-form-item-gi label="文件类别" path="docCategoryId" :span="24">
                    <n-tree-select
                        class="w-full"
                        v-model:value="data.docCategoryId"
                        :options="categoryOptions"
                        filterable
                        clearable
                        show-path
                        placeholder="请选择文件类别"
                    />
                </n-form-item-gi>
                <n-form-item-gi v-if="id" label="文件编号" path="no" :span="12">
                    <n-input v-model:value="data.no" placeholder="请输入文件编号" readonly />
                </n-form-item-gi>
                <n-form-item-gi v-if="id" label="版本/版次" path="versionNo" :span="12">
                    <n-input v-model:value="data.versionNo" placeholder="请输入版本/版次" readonly />
                </n-form-item-gi>
                <n-form-item-gi label="编制部门" path="departmentIds" :span="12">
                    <n-tree-select
                        v-model:value="data.departmentIds"
                        :options="deptOptions"
                        filterable
                        clearable
                        multiple
                        placeholder="请选择编制部门"
                        @update:value="handleDeptUpdate"
                    />
                </n-form-item-gi>
                <n-form-item-gi label="编制人" path="authorIds" :span="12">
                    <n-select
                        v-model:value="data.authorIds"
                        :options="compilerOptions"
                        placeholder="请选择编制人"
                        multiple
                    />
                </n-form-item-gi>
                <n-form-item-gi label="发布日期" path="publishDate" :span="12">
                    <n-date-picker
                        v-model:value="data.publishDate"
                        type="date"
                        style="width: 100%"
                        placeholder="请选择发布日期"
                    />
                </n-form-item-gi>
                <n-form-item-gi label="实施日期" path="effectiveDate" :span="12">
                    <n-date-picker
                        v-model:value="data.effectiveDate"
                        type="date"
                        style="width: 100%"
                        placeholder="请选择实施日期"
                    />
                </n-form-item-gi>
                <n-form-item-gi label="原文件编号" path="originalNo" :span="12">
                    <n-input v-model:value="data.originalNo" placeholder="请输入原文件编号" maxlength="50" show-count />
                </n-form-item-gi>
                <n-form-item-gi label="原版本/版次" path="originalVersionNo" :span="12">
                    <n-input
                        v-model:value="data.originalVersionNo"
                        placeholder="请输入原版本/版次"
                        maxlength="50"
                        show-count
                    />
                </n-form-item-gi>
                <n-form-item-gi label="上传文件" :span="24">
                    <minio-upload
                        class="w-full"
                        v-model:file-list="fileList"
                        :upload-props="{ max: 1, accept: '.pdf' }"
                    >
                        <template #drag>
                            <div class="w-full h-full flex-v justify-center items-center p-10px">
                                <img :src="uploadFileImg" class="w-40px h-40px mt-10px" />
                                <span class="text-14px mt-10px">点击或者拖动文件到该区域来上传</span>
                                <span class="text-12px c-#838383 mt-10px">只支持上传.pdf文件</span>
                            </div>
                        </template>
                    </minio-upload>
                </n-form-item-gi>
                <n-form-item-gi label="备注" :span="24">
                    <n-input v-model:value="data.remark" placeholder="请输入备注内容" />
                </n-form-item-gi>
                <n-form-item-gi label="相关文件" path="relatedFiles" :span="24"> </n-form-item-gi>
                <n-form-item-gi label="相关表单" path="relatedForms" :span="24"> </n-form-item-gi>
            </n-grid>
        </n-form>
    </alert-content>
</template>
<script setup lang="ts">
import { ref } from 'vue';
import minioUpload from '@/components/business/minio-upload.vue';
import uploadFileImg from '@/assets/images/file/upload-file.webp';
import { FormRules } from 'naive-ui';

const props = withDefaults(
    defineProps<{
        id?: string;
        type?: string;
        categoryOptions?: Record<string, any>[];
        deptOptions?: Record<string, any>[];
    }>(),
    {
        id: '',
        type: 'detail'
    }
);
const emit = defineEmits(['update:id']);
const { id } = useVModels(props, emit);

const isDetail = computed(() => props.type === 'detail');

const formRef = ref();
const data = ref<Record<string, any>>({});
const rules: FormRules = {
    no: { required: true, message: '请输入文件编号', trigger: ['blur', 'input'] },
    versionNo: { required: true, message: '请输入版本', trigger: ['blur', 'input'] },
    name: { required: true, message: '请输入文件名称', trigger: ['blur', 'input'] },
    docCategoryId: { required: true, message: '请选择文件类别', trigger: 'change' },
    departmentIds: { required: true, message: '请选择编制部门', trigger: 'change', type: 'array' },
    authorIds: { required: true, message: '请选择编制人', trigger: 'change', type: 'array' },
    publishDate: { required: true, message: '请选择发布日期', trigger: 'change', type: 'date' },
    effectiveDate: { required: true, message: '请选择实施日期', trigger: 'change', type: 'date' }
};

// 编制人Options
const compilerOptions = ref([]);
const initCompilerOptions = async (value: string) => {
    const res = await api.sass.api.v1.organizationUserInfo.list({
        organizationId: value
    });
    compilerOptions.value = res.data.data.map((item: any) => ({
        label: item.nickname,
        value: item.id
    }));
};
const handleDeptUpdate = async (value: string) => {
    data.value.authorIds = null;
    initCompilerOptions(value);
};

// 获取详情
const getDetail = async () => {
    const res = await $apis.nebula.api.v1.internal.get(id.value);
    data.value = res.data;
    if (data.value.fileId) {
        const file = await api.file.api.v1.file.get(data.value.fileId);
        fileList.value = [
            {
                id: file.data.id,
                name: file.data.name,
                status: 'finished'
            }
        ];
    }

    initCompilerOptions(data.value.departmentIds);
};

const fileList = ref<any[]>([]);

// 新增/编辑 提交
const submit = async () => {
    await formRef.value
        ?.validate()
        .then(async () => {
            await new Promise((resolve) => {
                window.$dialog.warning({
                    title: `确认${props.type === 'add' ? '新增' : '修订'}`,
                    content: `确认后将${props.type === 'add' ? '新增' : '修订'}内部文件，是否确认？`,
                    positiveText: '确认',
                    negativeText: '取消',
                    onPositiveClick: async () => {
                        if (fileList.value.length > 0) {
                            data.value.fileId = fileList.value[0]?.response?.id
                                ? fileList.value[0]?.response?.id
                                : data.value.fileId;
                        } else {
                            data.value.fileId = null;
                        }
                        if (id.value) {
                            await $apis.nebula.api.v1.internal.change(data.value);
                        } else {
                            await $apis.nebula.api.v1.internal.create(data.value);
                        }
                        window.$message.success(id.value ? '编辑成功' : '新增成功');
                        resolve(true);
                    }
                });
            });
        })
        .catch((err: any) => {
            window.$message.error(err[0][0].message);
            return Promise.reject();
        });
};

onMounted(() => {
    if (id.value) {
        getDetail();
    }
});
</script>
