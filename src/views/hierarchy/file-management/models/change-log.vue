<template>
    <div class="change-log">
        <n-data-table :columns="columns" :data="data" size="small" :pagination="pagination" scroll-x="1200px">
            <template #departmentNames="{ row }">
                <span>{{ row.departmentNames?.join(',') }}</span>
            </template>
            <template #authorNames="{ row }">
                <span>{{ row.authorNames?.join(',') }}</span>
            </template>
            <template #table_auditors="{ row }">
                <n-space vertical size="small">
                    <span v-for="item in row.approvalInfo?.auditors" :key="item.id">
                        {{ `${item.userNickname}（${dayjs(item.passedDate).format('YYYY-MM-DD')}）` }}
                    </span>
                    <span v-if="row.approvalInfo?.auditors.length === 0" class="text-gray-400"> - </span>
                </n-space>
            </template>
            <template #table_approvers="{ row }">
                <n-space vertical size="small">
                    <span v-for="item in row.approvalInfo?.approvers" :key="item.id">
                        {{ `${item.userNickname}（${dayjs(item.passedDate).format('YYYY-MM-DD')}）` }}
                    </span>
                    <span v-if="row.approvalInfo?.approvers.length === 0" class="text-gray-400"> - </span>
                </n-space>
            </template>
            <template #todo="{ row }">
                <n-button type="primary" size="tiny" @click="handleView(row)">详情</n-button>
            </template>
        </n-data-table>
    </div>
</template>

<script setup lang="ts">
import { PaginationProps } from 'naive-ui';
import { TableColumns } from 'naive-ui/es/data-table/src/interface';
import dayjs from 'dayjs';

const props = withDefaults(
    defineProps<{
        id?: string;
    }>(),
    {
        id: ''
    }
);
const emit = defineEmits(['update:id']);
const { id } = useVModels(props, emit);

const columns = ref<TableColumns>([
    {
        title: '序号',
        key: 'index',
        align: 'center',
        width: 60
    },
    {
        title: '文件编号',
        key: 'documentNo',
        align: 'center',
        minWidth: 200,
        fixed: 'left',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '版本/版次',
        key: 'documentVersion',
        align: 'center',
        width: 80
    },
    {
        title: '文件名称',
        key: 'documentName',
        align: 'center',
        minWidth: 200,
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '文件类别',
        key: 'documentCategory',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '编制部门',
        key: 'departmentNames',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '编制人',
        key: 'authorNames',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '审核人',
        key: 'auditors',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '批准人',
        key: 'approvers',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '发布日期',
        key: 'publishDate',
        align: 'center',
        width: 120,
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '实施日期',
        key: 'effectiveDate',
        align: 'center',
        width: 120,
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '操作类型',
        key: 'operationType',
        align: 'center',
        width: 80,
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '查看详情',
        key: 'todo',
        align: 'center',
        fixed: 'right',
        width: 80
    }
]);

const data = ref([]);

// const getChangeLog = async () => {
//     const res = await $apis.nebula.api.v1.internal.log({ documentId: id.value, noPage: true });
//     data.value = res.data.data;
// };

const pagination = ref<PaginationProps>({
    pageSize: 10,
    prefix: ({ itemCount }) => `共 ${itemCount} 条`
});

const handleView = (row: any) => {
    window.$message.info(`变更记录：${row.time}`);
};

onMounted(() => {
    console.log(id.value);
    // getChangeLog();
});
</script>

<style scoped lang="less"></style>
