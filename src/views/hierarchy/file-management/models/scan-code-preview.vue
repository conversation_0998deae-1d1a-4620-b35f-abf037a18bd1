<template>
    <alert-content :show-default-buttons="false">
        <div class="flex-center flex-v">
            <span class="py-5px"> 文件：{{ file.name }}.pdf </span>
            <n-qr-code v-model="qrCode" :qr-props="qrProps" :padding="0" :borderSize="0" />
        </div>
    </alert-content>
</template>

<script setup lang="ts">
import useStore from '@/store/modules/main';

const props = defineProps<{
    file: { id: string; name: string };
}>();

const store = useStore();
const qrCode = ref('');
const qrProps = ref({
    size: 180
});

onMounted(async () => {
    qrCode.value = `${window.location.origin}${import.meta.env.PROD ? '/front' : ''}/#/mobile-file-preview?token=${
        store.token
    }&fileId=${props.file.id}&user=${store.userInfo.nickname}`;
    console.log('🚀 ~ qrCode.value:', qrCode.value);
});
</script>

<style scoped lang="less"></style>
