<template>
    <div class="distribute-file pr-10px">
        <n-grid :cols="2" :x-gap="10" :y-gap="10">
            <n-grid-item class="flex-v items-center">
                <span class="text-16px font-bold">内发：电子文件 - 查阅</span>
                <grant-recycle
                    :params="{
                        fileId: row.id,
                        fileForm: 1,
                        filePermission: 1
                    }"
                    :tree-data="treeData"
                    :record-data="recordData"
                    :selected-list="checkTreeData"
                    @grant="(persons) => onTransfer(1, persons)"
                    @recycle="(keys: string[]) => onBack(1, keys)"
                />
            </n-grid-item>
            <n-grid-item class="flex-v items-center">
                <span class="text-16px font-bold">内发：电子文件 - 查阅/下载</span>
                <grant-recycle
                    :params="{
                        fileId: row.id,
                        fileForm: 1,
                        filePermission: 2
                    }"
                    :tree-data="treeData"
                    :record-data="recordData"
                    :selected-list="checkTreeData"
                    @grant="(persons) => onTransfer(2, persons)"
                    @recycle="(keys: string[]) => onBack(2, keys)"
                />
            </n-grid-item>
            <n-grid-item class="flex-v items-center">
                <span class="text-16px font-bold">内发：纸质文件 - 一次下载</span>
                <grant-recycle
                    :params="{
                        fileId: row.id,
                        fileForm: 2,
                        filePermission: 3
                    }"
                    :tree-data="treeData"
                    :record-data="recordData"
                    :selected-list="checkTreeData"
                    @grant="(persons) => onTransfer(3, persons)"
                    @recycle="(keys: string[]) => onBack(3, keys)"
                />
            </n-grid-item>
            <n-grid-item class="flex-v items-center">
                <span class="text-16px font-bold">外发：电子文件 - 一次下载</span>
                <grant-recycle
                    :params="{
                        fileId: row.id,
                        fileForm: 1,
                        filePermission: 3
                    }"
                    :tree-data="treeData"
                    :record-data="recordData"
                    :selected-list="checkTreeData"
                    :hide-recycle-button="true"
                    @grant="(persons) => onTransfer(4, persons)"
                />
            </n-grid-item>
        </n-grid>
        <div class="flex-v items-start mt-10px">
            <p class="text-14px font-500 mt-20px">发放回收记录</p>
            <n-search-table-page
                :data-api="$apis.nebula.api.v1.documentLibrary.document.distributeRecords"
                :data-table-props="{
                    columns: operateColumns,
                    size: 'small'
                }"
                :search-props="{ show: false }"
                :params="params"
                :page-config="{
                    size: 3
                }"
            >
                <template #table_internalElectronicQuery="{ row }">
                    <n-popover trigger="hover" placement="top" :show-arrow="true">
                        <template #trigger>
                            <div
                                style="max-width: 400px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap"
                            >
                                <p v-html="formatPerson(row.internalElectronicQuery)"></p>
                            </div>
                        </template>
                        <div class="min-w-300px max-w-400px">
                            <div class="text-sm font-medium mb-2 text-gray-700">内发：电子文件-查阅人员清单</div>
                            <div class="space-y-2 max-h-200px overflow-y-auto">
                                <template v-if="row.internalElectronicQuery && row.internalElectronicQuery.length > 0">
                                    <template v-for="(item, idx) in row.internalElectronicQuery" :key="idx">
                                        <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                            <span class="font-medium text-gray-800">{{ item.userNickname }}</span>
                                            <div class="flex gap-1">
                                                <n-tag :type="getUserStatusTagType(item.status)" size="small" round>
                                                    {{ getUserStatusText(item.status) }}
                                                </n-tag>
                                            </div>
                                        </div>
                                    </template>
                                </template>
                                <template v-else>
                                    <div class="flex items-center justify-center p-4 text-gray-500">暂无数据</div>
                                </template>
                            </div>
                        </div>
                    </n-popover>
                </template>
                <template #table_internalElectronicDownload="{ row }">
                    <n-popover trigger="hover" placement="top" :show-arrow="true">
                        <template #trigger>
                            <div
                                style="max-width: 400px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap"
                            >
                                <p v-html="formatPerson(row.internalElectronicDownload)"></p>
                            </div>
                        </template>
                        <div class="min-w-300px max-w-400px">
                            <div class="text-sm font-medium mb-2 text-gray-700">内发：电子文件-查阅/下载人员清单</div>
                            <div class="space-y-2 max-h-200px overflow-y-auto">
                                <template
                                    v-if="row.internalElectronicDownload && row.internalElectronicDownload.length > 0"
                                >
                                    <template v-for="(item, idx) in row.internalElectronicDownload" :key="idx">
                                        <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                            <span class="font-medium text-gray-800">{{ item.userNickname }}</span>
                                            <div class="flex gap-1">
                                                <n-tag :type="getUserStatusTagType(item.status)" size="small" round>
                                                    {{ getUserStatusText(item.status) }}
                                                </n-tag>
                                            </div>
                                        </div>
                                    </template>
                                </template>
                                <template v-else>
                                    <div class="flex items-center justify-center p-4 text-gray-500">暂无数据</div>
                                </template>
                            </div>
                        </div>
                    </n-popover>
                </template>

                <template #table_applyTime="{ row }">
                    <n-time :time="row.applyTime" format="yyyy-MM-dd HH:mm:ss" />
                </template>
                <template #table_todo="{ row }">
                    <n-button type="primary" size="tiny" @click="handleApproval(row)">详情</n-button>
                </template>
            </n-search-table-page>

            <p class="text-14px font-500 mt-20px">内发：纸质文件-一次下载变更记录</p>
            <n-search-table-page
                class="w-full"
                :data-api="$apis.nebula.api.v1.documentLibrary.document.paperDownloadRecords"
                :data-table-props="{
                    columns: changeColumns,
                    size: 'small',
                    scrollX: 1200
                }"
                :search-props="{ show: false }"
                :params="params"
                :page-config="{
                    size: 3
                }"
            >
                <template #table_paperFileStatus="{ row }">
                    <n-tag
                        :type="paperFileStatusOptions[row.paperFileStatus - 1].type"
                        size="small"
                        :bordered="false"
                        >{{ paperFileStatusOptions[row.paperFileStatus - 1].label }}</n-tag
                    >
                </template>
                <template #table_fileDisposeStatus="{ row }">
                    <n-tag
                        v-if="paperFileStatusOptions[row.paperFileStatus - 1].value !== 1"
                        @click="
                            fileDisposeStatusOptions[row.fileDisposeStatus - 1].value == 1 ? handleDispose(row) : ''
                        "
                        :type="fileDisposeStatusOptions[row.fileDisposeStatus - 1].type"
                        size="small"
                        :bordered="false"
                        >{{ fileDisposeStatusOptions[row.fileDisposeStatus - 1].label
                        }}{{
                            fileDisposeStatusOptions[row.fileDisposeStatus - 1].value == 1 ? '(点击处置)' : ''
                        }}</n-tag
                    >
                </template>
                <template #table_distributeApplyTime="{ row }">
                    <n-time :time="row.distributeApplyTime" format="yyyy-MM-dd HH:mm:ss" />
                </template>
                <template #table_recycleApplyTime="{ row }">
                    <n-time v-if="row.recycleApplyTime" :time="row.recycleApplyTime" format="yyyy-MM-dd HH:mm:ss" />
                    <span v-else></span>
                </template>
                <template #table_todo="{ row }">
                    <n-button type="primary" size="tiny" @click="handleApproval(row, '纸质文件-一次下载')"
                        >详情</n-button
                    >
                </template>
            </n-search-table-page>

            <p class="text-14px font-500 mt-20px">外发：电子文件-一次下载变更记录</p>
            <n-search-table-page
                :data-api="$apis.nebula.api.v1.documentLibrary.document.electronicDownloadRecords"
                :data-table-props="{
                    columns: outDownloadColumns,
                    size: 'small'
                }"
                :search-props="{ show: false }"
                :params="params"
                :page-config="{
                    size: 3
                }"
            >
                <template #table_distributeApplyTime="{ row }">
                    <n-time :time="row.distributeApplyTime" format="yyyy-MM-dd HH:mm:ss" />
                </template>
                <template #table_isDownloaded="{ row }">
                    <n-tag :type="row.isDownloaded ? 'success' : 'error'" size="small" :bordered="false">{{
                        row.isDownloaded ? '是' : '否'
                    }}</n-tag>
                </template>
                <template #table_todo="{ row }">
                    <n-button type="primary" size="tiny" @click="handleApproval(row, '电子文件-一次下载')"
                        >详情</n-button
                    >
                </template>
            </n-search-table-page>

            <p class="text-14px font-500 mt-20px">借阅记录</p>
            <n-search-table-page
                :data-api="$apis.nebula.api.v1.documentLibrary.document.borrowRecords"
                :data-table-props="{
                    columns: borrowColumns,
                    size: 'small'
                }"
                :search-props="{ show: false }"
                :params="params"
                :page-config="{
                    size: 3
                }"
            >
                <template #table_approvalApplyTime="{ row }">
                    <n-time :time="row.approvalApplyTime" format="yyyy-MM-dd HH:mm:ss" />
                </template>
                <template #table_borrowPeriod="{ row }">
                    <span>
                        {{ dayjs(row.borrowTime).format('YYYY-MM-DD') }} 至
                        {{ dayjs(row.dueTime).format('YYYY-MM-DD') }}
                    </span>
                </template>
                <template #table_todo="{ row }">
                    <n-button type="primary" size="tiny" @click="handleApproval(row)">详情</n-button>
                </template>
            </n-search-table-page>
        </div>
    </div>
</template>

<script setup lang="ts">
import { NTag } from 'naive-ui';
import { TableColumns } from 'naive-ui/es/data-table/src/interface';
import dayjs from 'dayjs';

const props = withDefaults(
    defineProps<{
        row?: any;
        type?: string;
    }>(),
    {
        row: {},
        type: 'interior'
    }
);
const emit = defineEmits(['update:id']);
const { row } = useVModels(props, emit);
const checkTreeData = ref([]);
const recordData = ref([]);
const treeData = ref([]);

const onTransfer = async (type: number, persons: { key: string; label: string }[]) => {
    $alert.dialog({
        title: '提示',
        width: '500px',
        content: import('./distribute-file-grant-reason.vue'),
        props: {
            row: props.row,
            type,
            pType: props.type,
            persons,
            onSave: () => refreshData()
        }
    });
};

const onBack = async (type: number, keys: string[]) => {
    $alert.dialog({
        title: '提示',
        width: '500px',
        content: import('./distribute-file-recycle-reason.vue'),
        props: {
            row: props.row,
            type,
            pType: props.type,
            keys,
            recordData: recordData.value,
            onSave: () => refreshData()
        }
    });
};

const formatPerson = (person: any) => {
    const statusMap: Record<number, { text: string; color: string }> = {
        1: { text: '未签收', color: '#999' }, // 灰色
        2: { text: '已签收', color: '#005EFF' }, // 蓝色
        3: { text: '已回收', color: '#005EFF' } // 蓝色
    };
    if (!Array.isArray(person) || person.length === 0) return '';
    return person
        .map((item: any) => {
            const status = statusMap[item.status] || { text: '', color: '#005EFF' };
            return `${item.userNickname}<span style='color:${status.color};margin-left:4px;font-size:12px;'>（${status.text}）</span>`;
        })
        .join('，');
};

// 获取用户状态文本
const getUserStatusText = (status?: number) => {
    switch (status) {
        case 1:
            return '未签收';
        case 2:
            return '已签收';
        case 3:
            return '已回收';
        case 4:
            return '已处置';
        default:
            return '';
    }
};

// 获取用户状态标签类型
const getUserStatusTagType = (status?: number) => {
    switch (status) {
        case 1:
            return 'default'; // 未签收 - 灰色
        case 2:
            return 'info'; // 已签收 - 蓝色
        case 3:
            return 'info'; // 已回收 - 蓝色
        case 4:
            return 'info'; // 已处置 - 蓝色
        default:
            return 'default';
    }
};

const handleDispose = async (row: any) => {
    console.log(row);

    const res = await $apis.nebula.api.v1.issuanceApplication.getDetail(row.distributeRecordId);
    console.log(res);
    $alert.dialog({
        title: '纸质文件处置',
        width: '60%',
        content: import('../issuance-application/models/paper-disposal-form.vue'),
        props: {
            row: res.data,
            id: row.distributeRecordId,
            onSave: () => refreshData()
        }
    });
};

// 统一的刷新方法
const refreshData = async () => {
    await getUserTreeData();
    await getRecord();
};

/**
 * 表格配置
 */
const params = {
    documentId: props.row.id,
    documentVersionNo: props.row.versionNo || props.row.version
};

/**
 * 发放回收记录
 */
const operateColumns = ref<TableColumns>([
    {
        title: '操作时间',
        key: 'applyTime',
        align: 'center',
        width: 180
    },
    {
        title: '内发：电子文件-查阅',
        key: 'internalElectronicQuery',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '内发：电子文件-查阅/下载',
        key: 'internalElectronicDownload',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '变更人',
        key: 'applicantName',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '审批流程',
        key: 'todo',
        align: 'center',
        fixed: 'right',
        width: 80
    }
]);

/**
 * 内发：纸质文件-一次下载变更记录
 */
const changeColumns = ref<TableColumns>([
    {
        title: '纸质文件接收人',
        key: 'recipientUserName',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '纸质文件状态',
        key: 'paperFileStatus',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '文件处置状态',
        key: 'fileDisposeStatus',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '发放人',
        key: 'distributeApplicantName',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '发放申请时间',
        key: 'distributeApplyTime',
        align: 'center',
        width: 180
    },
    {
        title: '回收人',
        key: 'recycleApplicantName',
        align: 'center'
    },
    {
        title: '回收申请时间',
        key: 'recycleApplyTime',
        align: 'center',
        width: 180
    },
    {
        title: '审批流程',
        key: 'todo',
        align: 'center',
        fixed: 'right',
        width: 80
    }
]);
const paperFileStatusOptions: any[] = [
    {
        label: '未回收',
        value: 1,
        type: 'warning'
    },
    {
        label: '回收中',
        value: 2,
        type: 'info'
    },
    {
        label: '已回收',
        value: 3,
        type: 'success'
    }
];
const fileDisposeStatusOptions: any[] = [
    {
        label: '未处置',
        value: 1,
        type: 'warning'
    },
    {
        label: '处置中',
        value: 2,
        type: 'info'
    },
    {
        label: '已处置',
        value: 3,
        type: 'success'
    }
];

/**
 * 外发：电子文件-一次下载变更记录
 */
const outDownloadColumns = ref<TableColumns>([
    {
        title: '电子文件接收方',
        key: 'recipientUserName',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '发放人',
        key: 'distributeApplicantName',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '发放申请时间',
        key: 'distributeApplyTime',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '是否下载',
        key: 'isDownloaded',
        align: 'center',
        width: 80
    },
    {
        title: '审批流程',
        key: 'todo',
        align: 'center',
        fixed: 'right',
        width: 80
    }
]);

/**
 * 借阅记录
 */
const borrowColumns = ref<TableColumns>([
    {
        title: '操作时间',
        key: 'approvalApplyTime',
        align: 'center',
        width: 180
    },
    {
        title: '借阅人',
        key: 'userNickname',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '借阅期限',
        key: 'borrowPeriod',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '审批流程',
        key: 'todo',
        align: 'center',
        fixed: 'right',
        width: 80
    }
]);

// 审批详情
const handleApproval = (row: any, title?: string) => {
    $alert.dialog({
        title: '审批详情',
        content: import('./distribute-file-approval-process.vue'),
        width: '500px',
        props: {
            flowId: row.workflowId,
            title,
            row
        }
    });
};
const getRecord = async () => {
    const res = await $apis.nebula.api.v1.businessDictionary.node.getRecord({
        documentId: props.row.id
    });
    recordData.value = res.data.list;
};
const getUserTreeData = async () => {
    const res = await $apis.nebula.api.v1.issuanceApplication.getUserList({
        fileId: props.row.id
    });

    if (res.data?.organizationUserInfo) {
        treeData.value = res.data.organizationUserInfo;
    } else {
        treeData.value = [];
    }
};
onMounted(async () => {
    await Promise.all([getUserTreeData(), getRecord()]);
});
</script>

<style scoped lang="less"></style>
